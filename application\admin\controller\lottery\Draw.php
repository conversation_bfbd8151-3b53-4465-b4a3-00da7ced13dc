<?php

namespace app\admin\controller\lottery;

use app\common\controller\Backend;
use app\common\model\LotteryDraw;
use think\Db;
use think\Exception;

/**
 * 手动开奖管理
 */
class Draw extends Backend
{
    protected $model = null;
    protected $searchFields = 'lottery_type,period,admin_name';
    
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new LotteryDraw();
    }
    
    /**
     * 查看列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            
            $result = ['total' => $list->total(), 'rows' => $list->items()];
            return json($result);
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 添加开奖记录
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }
            
            // 验证号码格式
            if (!LotteryDraw::validateNumbers($params['numbers'], false)) {
                $this->error('开奖号码格式错误，请输入1-49之间的数字，用逗号分隔');
            }
            
            // 允许相同期数和时间，便于在同一时间点添加多个号码记录
            
            Db::startTrans();
            try {
                // 添加管理员信息
                $params['admin_id'] = $this->auth->id;
                $params['admin_name'] = $this->auth->username;

                // 自动设置开奖时间为当天22:00
                $params['draw_time'] = date('Y-m-d') . ' 22:00:00';

                // 自动生成下期信息
                $params['next_period'] = LotteryDraw::generateNextPeriod($params['period']);
                $params['next_draw_time'] = date('Y-m-d', strtotime('+1 day')) . ' 22:00:00'; // 下一天22:00

                // 设置默认备注
                if (empty($params['remark'])) {
                    $params['remark'] = '手动开奖';
                }
                
                $result = $this->model->save($params);
                if (!$result) {
                    throw new Exception('保存失败');
                }
                
                Db::commit();
                $this->success();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 编辑开奖记录
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }
            
            // 验证号码格式
            if (!LotteryDraw::validateNumbers($params['numbers'], false)) {
                $this->error('开奖号码格式错误，请输入1-49之间的数字，用逗号分隔');
            }
            
            // 允许相同期数和时间，便于在同一时间点编辑多个号码记录
            
            Db::startTrans();
            try {
                // 更新管理员信息
                $params['admin_id'] = $this->auth->id;
                $params['admin_name'] = $this->auth->username;

                // 编辑时保持原有开奖时间不变，只更新号码等其他信息
                // 不修改 draw_time 和 next_draw_time
                unset($params['draw_time']);
                unset($params['next_draw_time']);

                $result = $row->save($params);
                if (!$result) {
                    throw new Exception('保存失败');
                }
                
                Db::commit();
                $this->success();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }
    
    /**
     * 删除开奖记录
     */
    public function del($ids = null)
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        
        $ids = $ids ?: $this->request->post("ids");
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        
        $list = $this->model->where($pk, 'in', $ids)->select();
        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        
        if ($count) {
            $this->success();
        } else {
            $this->error(__('No rows were deleted'));
        }
    }
    
    /**
     * 快速开奖
     */
    public function quickdraw()
    {
        if ($this->request->isPost()) {
            $lotteryType = $this->request->post('lottery_type');
            $numbers = $this->request->post('numbers');
            
            if (!$lotteryType || !$numbers) {
                $this->error('参数不能为空');
            }
            
            // 验证号码格式
            if (!LotteryDraw::validateNumbers($numbers, false)) {
                $this->error('开奖号码格式错误，请输入1-49之间的数字，用逗号分隔');
            }
            
            Db::startTrans();
            try {
                // 获取最新期数
                $latest = LotteryDraw::getLatestByType($lotteryType);
                $currentPeriod = $latest ? LotteryDraw::generateNextPeriod($latest->period) : '001';
                
                $params = [
                    'lottery_type' => $lotteryType,
                    'period' => $currentPeriod,
                    'numbers' => is_array($numbers) ? implode(',', $numbers) : $numbers,
                    'draw_time' => date('Y-m-d') . ' 22:00:00',
                    'next_period' => LotteryDraw::generateNextPeriod($currentPeriod),
                    'next_draw_time' => date('Y-m-d', strtotime('+1 day')) . ' 22:00:00',
                    'status' => 1,
                    'admin_id' => $this->auth->id,
                    'admin_name' => $this->auth->username,
                    'remark' => '快速开奖'
                ];
                
                $result = $this->model->save($params);
                if (!$result) {
                    throw new Exception('保存失败');
                }
                
                Db::commit();
                $this->success('开奖成功');
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        
        return $this->view->fetch();
    }
}
