<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#t-all" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="t-all">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <a href="javascript:;" class="btn btn-info btn-quick-draw" title="快速开奖" ><i class="fa fa-bolt"></i> 快速开奖</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('lottery/draw/edit')}"
                           data-operate-del="{:$auth->check('lottery/draw/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(function () {
    var table = $("#table");

    // 初始化表格
    table.bootstrapTable({
        url: 'lottery/draw/index' + location.search,
        pk: 'id',
        sortName: 'id',
        sortOrder: 'desc',
        columns: [
            [
                {checkbox: true},
                {field: 'id', title: __('Id'), operate: false},
                {field: 'lottery_type', title: '彩种类型', searchList: {"sicai":"私彩"}, formatter: Table.api.formatter.normal},
                {field: 'period', title: '期数', operate: 'LIKE'},
                {field: 'numbers', title: '开奖号码', operate: false, formatter: function(value, row, index) {
                    var numbers = value.split(',');
                    var html = '';
                    for(var i = 0; i < numbers.length; i++) {
                        var color = 'red';
                        if(i < 6) {
                            html += '<span class="label label-primary" style="margin-right:2px;">' + numbers[i] + '</span>';
                        } else {
                            html += '<span class="label label-danger" style="margin-left:5px;">' + numbers[i] + '</span>';
                        }
                    }
                    return html;
                }},
                {field: 'draw_time', title: '开奖时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                {field: 'next_period', title: '下期期数', operate: false},
                {field: 'next_draw_time', title: '下期开奖时间', operate: false, formatter: Table.api.formatter.datetime},
                {field: 'status', title: '状态', searchList: {"0":"禁用","1":"启用"}, formatter: Table.api.formatter.status},
                {field: 'admin_name', title: '操作员', operate: 'LIKE'},
                {field: 'remark', title: '备注', operate: 'LIKE'},
                {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
            ]
        ]
    });

    // 为表格绑定事件
    Table.api.bindevent(table);
    
    // 快速开奖按钮
    $(document).on('click', '.btn-quick-draw', function() {
        Fast.api.open('lottery/draw/quickDraw', '快速开奖', {
            callback: function(data) {
                table.bootstrapTable('refresh');
            }
        });
    });
});
</script>
