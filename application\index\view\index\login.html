<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 530px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 登录页面 */
        .login-page {
            width: 300px;
            background: white;
            padding: 30px;
        }

        .login-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 25px;
            color: #333;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .login-input {
            padding: 8px 10px;
            border: 1px solid #ccc;
            font-size: 14px;
            outline: none;
        }

        .login-input:focus {
            border-color: #007bff;
        }

        .login-submit {
            padding: 10px 20px;
            background: #ffc107;
            color: #333;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
        }

        .login-submit:hover {
            background: #e0a800;
        }

        .register-link {
            text-align: center;
            margin-top: 15px;
        }

        .register-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        /* 消息提示样式 */
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .loading-message {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* 禁用状态 */
        .login-submit:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 登录页面 -->
        <div class="login-page">
            <div class="login-title">用户登录</div>
            <form class="login-form" onsubmit="submitLogin(event)">
                <div class="form-group">
                    <label>用户名/邮箱/手机号</label>
                    <input type="text" id="loginAccount" class="login-input" placeholder="请输入用户名、邮箱或手机号" required>
                </div>
                <div class="form-group">
                    <label>密&nbsp;&nbsp;&nbsp;&nbsp;码</label>
                    <input type="password" id="loginPassword" class="login-input" placeholder="请输入密码" required>
                </div>
                <div id="loginMessage"></div>
                <button type="submit" id="loginBtn" class="login-submit">登录</button>
            </form>
            <div class="register-link">
                <a href="/index/index/register">马上注册</a> |
                <a href="/">返回首页</a>
            </div>
        </div>
    </div>

    <script>
        // 显示消息
        function showMessage(message, type = 'error') {
            const messageDiv = document.getElementById('loginMessage');
            messageDiv.innerHTML = `<div class="message ${type}-message">${message}</div>`;

            // 3秒后自动清除消息
            if (type !== 'loading') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 3000);
            }
        }

        // 设置按钮状态
        function setButtonState(disabled, text) {
            const btn = document.getElementById('loginBtn');
            btn.disabled = disabled;
            btn.textContent = text;
        }

        // 提交登录
        async function submitLogin(event) {
            event.preventDefault();

            const account = document.getElementById('loginAccount').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!account || !password) {
                showMessage('请输入用户名和密码');
                return;
            }

            // 显示加载状态
            setButtonState(true, '登录中...');
            showMessage('正在登录，请稍候...', 'loading');

            try {
                const response = await fetch('/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        account: account,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.code === 1) {
                    // 登录成功
                    showMessage('登录成功！正在跳转...', 'success');

                    // 保存用户信息和token
                    if (result.data && result.data.userinfo) {
                        localStorage.setItem('userToken', result.data.userinfo.token);
                        localStorage.setItem('userInfo', JSON.stringify(result.data.userinfo));
                    }

                    // 2秒后跳转到首页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    // 登录失败
                    showMessage(result.msg || '登录失败，请检查用户名和密码');
                    setButtonState(false, '登录');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                showMessage('网络错误，请稍后重试');
                setButtonState(false, '登录');
            }
        }
    </script>
</body>
</html>
