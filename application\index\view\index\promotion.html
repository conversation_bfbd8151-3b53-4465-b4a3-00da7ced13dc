<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$site.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 530px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #1e0a0a;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
        }

        .back-btn img {
            width: auto;
            height: 20px;
            vertical-align: middle;
            filter: brightness(0) invert(1);
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 未点击状态 - 灰色毛玻璃 */
        .tab-item:not(.active) {
            background: rgba(153, 153, 153, 0.8);
            color: white;
        }

        /* 点击状态颜色 - 毛玻璃效果 */
        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8); /* 绿色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8); /* 蓝色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8); /* 红色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8); /* 黑色毛玻璃 */
            color: white;
        }

        /* 悬停效果 */
        .tab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 开奖记录按钮 */
        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .record-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.4);
        }

        /* 号码球区域 */
        .balls-section {
            width: 100%;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .balls-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: nowrap;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 40px;
            flex-shrink: 0;
        }

        .ball-wrapper {
            position: relative;
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: calc(50% - 2px);
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .ball-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 加号分隔符 */
        .plus-separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
            margin: 0 2px;
            flex-shrink: 0;
        }

        /* 开奖时间区域 */
        .draw-time-section {
            width: 100%;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draw-time-text {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .draw-time-text .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 刷新按钮 */
        .refresh-btn {
            padding: 3px 10px;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
            outline: none;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
        }

        /* 蓝色长条 */
        .blue-bar {
            width: 100%;
            height: 20px;
            background: #a9d5e9;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .blue-bar-theme {
            color: #0b78ff;
            font-size: 10px;
            font-weight: 600;
            margin-right: 6px;
            white-space: nowrap;
        }

        .blue-bar-title {
            color: black;
            font-size: 10px;
            font-weight: 500;
            flex: 1;
        }

        .blue-bar-right {
            display: flex;
            align-items: center;
            font-size: 10px;
            white-space: nowrap;
        }

        .blue-bar-views {
            color: black;
            margin-right: 8px;
        }

        .view-count {
            color: red;
        }

        .blue-bar-close {
            color: black;
            text-decoration: none;
        }

        .blue-bar-close:hover {
            text-decoration: underline;
        }

        /* 跑马灯公告样式 */
        .marquee-notice {
            background: #000;
            border: 2px solid #00ff00;
            border-radius: 35px;
            padding: 10px;
            margin: 15px;
            overflow: hidden;
            white-space: nowrap;
        }

        .marquee-text {
            display: inline-block;
            animation: marquee 60s linear infinite, colorChange 4s infinite;
            font-size: 14px;
            font-weight: 500;
        }

        @keyframes marquee {
            0% {
                transform: translateX(50%);
            }
            100% {
                transform: translateX(-150%);
            }
        }

        @keyframes colorChange {
            0% { color: #ff0000; }
            16.66% { color: #ff8000; }
            33.33% { color: #ffff00; }
            50% { color: #00ff00; }
            66.66% { color: #0080ff; }
            83.33% { color: #8000ff; }
            100% { color: #ff0080; }
        }

        /* 内容区域 */
        .content-area {
            min-height: calc(100vh - 60px);
        }

        .placeholder-text {
            text-align: center;
            color: #666;
            font-size: 16px;
        }

        /* 手机端跑马灯优化 */
        @media (max-width: 767px) {
            .marquee-notice {
                margin: 10px;
                padding: 8px;
            }

            .marquee-text {
                font-size: 12px;
            }
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }
            
            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <a href="/" class="back-btn">
                <img src="/images/balk.png" alt="返回">
            </a>
            <div class="page-title">优惠活动</div>
        </div>

        <!-- 标签区域 -->
        <section class="tabs-section">
            <div class="tabs-container">
                <div class="tab-item tab-1 active" onclick="selectTab(1)">私彩1</div>
                <div class="tab-item tab-2" onclick="selectTab(2)">新澳</div>
                <div class="tab-item tab-3" onclick="selectTab(3)">香港</div>
                <div class="tab-item tab-4" onclick="selectTab(4)">老澳</div>
            </div>
        </section>

        <!-- 开奖信息区域 -->
        <section class="lottery-info">
            <div class="lottery-text" id="lotteryText">
                私彩 第<span class="period-number">200</span>期开奖结果:
            </div>
            <button class="record-btn" onclick="showRecord()">开奖记录</button>
        </section>

        <!-- 号码球区域 -->
        <section class="balls-section">
            <div class="balls-container" id="ballsContainer">
                <!-- 7个号码球将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 开奖时间区域 -->
        <section class="draw-time-section">
            <div class="draw-time-text" id="drawTimeText">
                第<span class="period-number">200</span>期开奖时间07月28日 周一21点32分
            </div>
            <button class="refresh-btn" onclick="refreshData()">刷新</button>
        </section>

        <!-- 蓝色长条 -->
        <div class="blue-bar">
            <span class="blue-bar-theme">主题:</span>
            <span class="blue-bar-title">{$site.title}</span>
            <div class="blue-bar-right">
                <span class="blue-bar-views">阅读<span class="view-count">27512</span>次</span>
                <span class="blue-bar-separator"> | </span>
                <a href="javascript:void(0)" class="blue-bar-close" onclick="closePage()">关闭本页</a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="placeholder-text">
                {$site.zhanshi1}
            </div>

            <!-- 跑马灯公告 -->
            <div class="marquee-notice">
                <div class="marquee-text">{$site.notio}</div>
            </div>

            {$site.zhanshi2}
        </div>
    </div>

    <script>
        // 开奖数据缓存
        let amLotteryData = null;  // 新澳门
        let xgLotteryData = null;  // 香港
        let oldAmLotteryData = null;  // 老澳门

        // 轮询相关变量
        let pollingInterval = null;  // 轮询定时器
        let currentActiveTab = 1;    // 当前激活的标签

        function selectTab(tabNumber) {
            // 移除所有标签的active类
            const allTabs = document.querySelectorAll('.tab-item');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 给点击的标签添加active类
            const selectedTab = document.querySelector(`.tab-${tabNumber}`);
            selectedTab.classList.add('active');

            // 更新当前激活标签并重新开始轮询
            currentActiveTab = tabNumber;
            stopPolling(); // 先停止之前的轮询

            // 根据不同标签获取对应的真实数据
            if (tabNumber === 1) {
                // 私彩使用手动开奖数据
                fetchManualData('sicai').then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `私彩 第<span class="period-number">200</span>期开奖结果:`;
                        updateBalls(tabNumber);
                        updateDrawTime(tabNumber);
                    }
                });
            } else if (tabNumber === 2) {
                // 新澳门 - 立即获取最新数据
                fetchAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `新澳门 第<span class="period-number">--</span>期开奖结果:`;
                    }
                });
            } else if (tabNumber === 3) {
                // 香港 - 立即获取最新数据
                fetchXgLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `香港 第<span class="period-number">--</span>期开奖结果:`;
                    }
                });
            } else if (tabNumber === 4) {
                // 老澳门 - 立即获取最新数据
                fetchOldAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `老澳门 第<span class="period-number">--</span>期开奖结果:`;
                    }
                });
            }

            // 开始轮询当前标签的数据
            startPolling();
        }

        function updateBalls(tabNumber) {
            const ballsContainer = document.getElementById('ballsContainer');
            const ballsData = {
                1: [
                    { number: '08', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '15', zodiac: '兔', element: '木', color: 'green' },
                    { number: '23', zodiac: '猪', element: '火', color: 'red' },
                    { number: '31', zodiac: '马', element: '火', color: 'red' },
                    { number: '42', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '07', zodiac: '牛', element: '土', color: 'green' },
                    { number: '19', zodiac: '羊', element: '土', color: 'red' }
                ],
                2: [
                    { number: '03', zodiac: '兔', element: '木', color: 'green' },
                    { number: '12', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '25', zodiac: '龙', element: '土', color: 'red' },
                    { number: '34', zodiac: '虎', element: '木', color: 'green' },
                    { number: '41', zodiac: '马', element: '火', color: 'red' },
                    { number: '06', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '18', zodiac: '鸡', element: '金', color: 'blue' }
                ],
                3: [
                    { number: '01', zodiac: '猪', element: '火', color: 'red' },
                    { number: '14', zodiac: '虎', element: '木', color: 'green' },
                    { number: '27', zodiac: '兔', element: '木', color: 'green' },
                    { number: '33', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '45', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '09', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '22', zodiac: '虎', element: '木', color: 'green' }
                ],
                4: [
                    { number: '05', zodiac: '马', element: '火', color: 'red' },
                    { number: '16', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '28', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '37', zodiac: '猪', element: '火', color: 'red' },
                    { number: '44', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '11', zodiac: '猪', element: '火', color: 'red' },
                    { number: '29', zodiac: '马', element: '火', color: 'red' }
                ]
            };

            const balls = ballsData[tabNumber];
            ballsContainer.innerHTML = '';

            balls.forEach((ball, index) => {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ball.color}.png" alt="${ball.color} ball" class="ball-image">
                        <div class="ball-number">${ball.number}</div>
                    </div>
                    <div class="ball-info">${ball.zodiac}/${ball.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                // 在第6个球后添加加号分隔符
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });
        }

        function updateDrawTime(tabNumber) {
            const drawTimeText = document.getElementById('drawTimeText');
            const drawTimes = {
                1: { period: '200', time: '开奖时间07月28日 周一21点32分' },
                2: { period: '156', time: '开奖时间07月28日 周一20点15分' },
                3: { period: '089', time: '开奖时间07月28日 周一19点45分' },
                4: { period: '234', time: '开奖时间07月28日 周一22点10分' }
            };

            const drawTime = drawTimes[tabNumber];
            drawTimeText.innerHTML = `第<span class="period-number">${drawTime.period}</span>期${drawTime.time}`;
        }

        // 获取新澳门开奖数据
        async function fetchAmLotteryData() {
            try {
                const response = await fetch('/api/lottery/getAmData');
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    amLotteryData = result.data;
                    console.log('新澳门数据:', amLotteryData);
                    return amLotteryData;
                } else {
                    console.error('获取新澳门数据失败:', result.msg || '未知错误');
                }
            } catch (error) {
                console.error('获取新澳门数据失败:', error);
            }
            return null;
        }

        // 获取香港开奖数据
        async function fetchXgLotteryData() {
            try {
                const response = await fetch('/api/lottery/getXgData');
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    xgLotteryData = result.data;
                    console.log('香港数据:', xgLotteryData);
                    return xgLotteryData;
                } else {
                    console.error('获取香港数据失败:', result.msg || '未知错误');
                }
            } catch (error) {
                console.error('获取香港数据失败:', error);
            }
            return null;
        }

        // 获取老澳门开奖数据
        async function fetchOldAmLotteryData() {
            try {
                const response = await fetch('/api/lottery/get48AmData');
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    oldAmLotteryData = result.data;
                    console.log('老澳门数据:', oldAmLotteryData);
                    return oldAmLotteryData;
                } else {
                    console.error('获取老澳门数据失败:', result.msg || '未知错误');
                }
            } catch (error) {
                console.error('获取老澳门数据失败:', error);
            }
            return null;
        }

        // 通用显示函数
        function updateLotteryDisplay(data) {
            if (!data) return;

            // 更新期数和标题
            const lotteryText = document.getElementById('lotteryText');
            lotteryText.innerHTML = `${data.title} 第<span class="period-number">${data.periods}</span>期开奖结果:`;

            // 更新号码球
            const ballsContainer = document.getElementById('ballsContainer');
            const numbers = data.num.split(',');
            ballsContainer.innerHTML = '';

            numbers.forEach((num, index) => {
                const ballData = getNumberAttributes(num.trim());

                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ballData.color}.png" alt="${ballData.color} ball" class="ball-image">
                        <div class="ball-number">${ballData.number}</div>
                    </div>
                    <div class="ball-info">${ballData.zodiac}/${ballData.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                // 只在第6个球后面添加加号分隔符（前6个是正码，第7个是特码）
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });

            // 更新下期开奖时间
            const drawTimeText = document.getElementById('drawTimeText');

            if (data.nextperiods && data.nexttime) {
                const nextTime = new Date(data.nexttime);
                const nextTimeStr = `${nextTime.getMonth() + 1}月${nextTime.getDate()}日 ${['周日','周一','周二','周三','周四','周五','周六'][nextTime.getDay()]}${nextTime.getHours()}点${nextTime.getMinutes().toString().padStart(2, '0')}分`;
                drawTimeText.innerHTML = `第<span class="period-number">${data.nextperiods}</span>期开奖时间${nextTimeStr}`;
            }
        }

        // 获取号码属性（生肖、五行、波色）
        function getNumberAttributes(number) {
            const num = parseInt(number);

            // 生肖映射 (1-49循环)
            const zodiacMap = ['', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
            const zodiac = zodiacMap[((num - 1) % 12) + 1];

            // 五行映射
            const elementMap = {
                1: '金', 2: '金', 7: '金', 8: '金', 12: '金', 13: '金', 18: '金', 19: '金', 23: '金', 24: '金', 29: '金', 30: '金', 34: '金', 35: '金', 40: '金', 41: '金', 45: '金', 46: '金',
                3: '水', 4: '水', 9: '水', 10: '水', 14: '水', 15: '水', 20: '水', 25: '水', 26: '水', 31: '水', 36: '水', 37: '水', 42: '水', 47: '水', 48: '水',
                5: '火', 6: '火', 11: '火', 16: '火', 17: '火', 21: '火', 22: '火', 27: '火', 28: '火', 32: '火', 33: '火', 38: '火', 39: '火', 43: '火', 44: '火', 49: '火',
                21: '木', 22: '木', 27: '木', 28: '木', 33: '木', 34: '木', 39: '木', 44: '木', 45: '木',
                2: '土', 7: '土', 12: '土', 17: '土', 18: '土', 23: '土', 29: '土', 30: '土', 35: '土', 40: '土', 41: '土', 46: '土'
            };
            const element = elementMap[num] || '土';

            // 波色映射
            let color;
            if ([1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46].includes(num)) {
                color = 'red';
            } else if ([3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48].includes(num)) {
                color = 'blue';
            } else {
                color = 'green';
            }

            return {
                number: number,
                zodiac: zodiac,
                element: element,
                color: color
            };
        }

        // 获取手动开奖数据（仅用于私彩）
        async function fetchManualData(lotteryType) {
            try {
                const response = await fetch(`/api/lottery/getManualData?type=${lotteryType}`);
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    console.log(`${lotteryType}手动开奖数据:`, result.data);
                    return result.data;
                } else {
                    console.error(`获取${lotteryType}手动开奖数据失败:`, result.msg || '未知错误');
                }
            } catch (error) {
                console.error(`获取${lotteryType}手动开奖数据失败:`, error);
            }
            return null;
        }



        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载私彩手动开奖数据
            fetchManualData('sicai').then(data => {
                if (data) {
                    updateLotteryDisplay(data);
                } else {
                    // 获取失败时的默认显示
                    updateBalls(1); // 默认显示私彩的球
                    updateDrawTime(1); // 默认显示私彩的开奖时间
                }
            });
        });

        function refreshData() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            if (tabNumber === 1) {
                // 私彩使用演示数据
                updateBalls(tabNumber);
                updateDrawTime(tabNumber);
                alert('私彩数据已刷新');
            } else if (tabNumber === 2) {
                // 新澳门
                fetchAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                        alert('新澳门数据已刷新');
                    } else {
                        alert('新澳门数据刷新失败');
                    }
                });
            } else if (tabNumber === 3) {
                // 香港
                fetchXgLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                        alert('香港数据已刷新');
                    } else {
                        alert('香港数据刷新失败');
                    }
                });
            } else if (tabNumber === 4) {
                // 老澳门
                fetchOldAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                        alert('老澳门数据已刷新');
                    } else {
                        alert('老澳门数据刷新失败');
                    }
                });
            }
        }

        // 开始轮询当前标签的数据
        function startPolling() {
            // 清除之前的轮询
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            // 只对有真实数据的标签进行轮询
            if (currentActiveTab === 1) {
                // 私彩不需要轮询
                return;
            }

            // 每3秒轮询一次，确保实时性
            pollingInterval = setInterval(() => {
                console.log(`轮询标签${currentActiveTab}的数据...`);

                if (currentActiveTab === 2) {
                    // 新澳门
                    fetchAmLotteryData().then(data => {
                        if (data) {
                            updateLotteryDisplay(data);
                            console.log('新澳门数据已更新');
                        }
                    });
                } else if (currentActiveTab === 3) {
                    // 香港
                    fetchXgLotteryData().then(data => {
                        if (data) {
                            updateLotteryDisplay(data);
                            console.log('香港数据已更新');
                        }
                    });
                } else if (currentActiveTab === 4) {
                    // 老澳门
                    fetchOldAmLotteryData().then(data => {
                        if (data) {
                            updateLotteryDisplay(data);
                            console.log('老澳门数据已更新');
                        }
                    });
                }
            }, 3000); // 3秒轮询一次，确保实时性
        }

        // 停止轮询
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                console.log('已停止轮询');
            }
        }

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时停止轮询
                stopPolling();
                console.log('页面隐藏，停止轮询');
            } else {
                // 页面显示时恢复轮询
                startPolling();
                console.log('页面显示，恢复轮询');
            }
        });

        function showRecord() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            let url = '';
            if (tabNumber === 1) {
                // 私彩跳转到专门的记录页面
                window.location.href = '/index/index/sicai_record';
                return;
            } else if (tabNumber === 2) {
                // 新澳门
                url = 'https://zhibo.77kj.vip/kj/index.html?am';
            } else if (tabNumber === 3) {
                // 香港
                url = 'https://zhibo.77kj.vip/kj/index.html?xg';
            } else if (tabNumber === 4) {
                // 老澳门
                url = 'https://zhibo.77kj.vip/kj/index.html?48am';
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        function closePage() {
            window.close();
        }
    </script>
</body>
</html>
