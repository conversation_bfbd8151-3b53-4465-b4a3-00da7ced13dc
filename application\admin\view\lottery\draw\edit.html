<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">彩种类型:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-lottery_type" data-rule="required" class="form-control selectpicker" name="row[lottery_type]">
                <option value="sicai" selected>私彩</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">期数:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-period" data-rule="required" class="form-control" name="row[period]" type="text" value="{$row.period}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">开奖号码:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-numbers" data-rule="required" class="form-control" name="row[numbers]" type="text" value="{$row.numbers}">
            <span class="help-block">请输入1-49之间的数字，用逗号分隔，可输入任意数量</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">开奖时间:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="form-control-static">{$row.draw_time} (每天晚上 22:00 开奖)</div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">状态:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="row[status]-1"><input id="row[status]-1" name="row[status]" type="radio" value="1" {:$row.status==1?'checked':''}> 启用</label> 
                <label for="row[status]-0"><input id="row[status]-0" name="row[status]" type="radio" value="0" {:$row.status==0?'checked':''}> 禁用</label> 
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" rows="3" name="row[remark]" cols="50">{$row.remark}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
$(function() {
    // 号码生成器
    $('#c-numbers').after('<button type="button" class="btn btn-info btn-sm" id="generate-numbers" style="margin-left:10px;">随机生成</button>');
    
    $('#generate-numbers').click(function() {
        var numbers = [];
        for(var i = 0; i < 7; i++) {
            var num = Math.floor(Math.random() * 49) + 1;
            numbers.push(num.toString().padStart(2, '0'));
        }
        $('#c-numbers').val(numbers.join(','));
    });
});
</script>
