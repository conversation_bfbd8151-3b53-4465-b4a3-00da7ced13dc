define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'lottery/draw/index' + location.search,
                    add_url: 'lottery/draw/add',
                    edit_url: 'lottery/draw/edit',
                    del_url: 'lottery/draw/del',
                    multi_url: 'lottery/draw/multi',
                    import_url: 'lottery/draw/import',
                    table: 'lottery_draws',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'lottery_type', title: '彩种类型', searchList: {"sicai":"私彩"}, formatter: function(value, row, index) {
                            var typeMap = {"sicai":"私彩"};
                            return typeMap[value] || value;
                        }},
                        {field: 'period', title: '期数', operate: 'LIKE'},
                        {field: 'numbers', title: '开奖号码', operate: false, formatter: function(value, row, index) {
                            if (!value) return '';
                            var numbers = value.split(',');
                            var html = '';
                            for(var i = 0; i < numbers.length; i++) {
                                if(i < 6) {
                                    html += '<span class="label label-primary" style="margin-right:2px;">' + numbers[i] + '</span>';
                                } else {
                                    html += '<span class="label label-danger" style="margin-left:5px;">' + numbers[i] + '</span>';
                                }
                            }
                            return html;
                        }},
                        {field: 'draw_time', title: '开奖时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'next_period', title: '下期期数', operate: false},
                        {field: 'next_draw_time', title: '下期开奖时间', operate: false, formatter: Table.api.formatter.datetime},
                        {field: 'status', title: '状态', searchList: {"0":"禁用","1":"启用"}, formatter: Table.api.formatter.status},
                        {field: 'admin_name', title: '操作员', operate: 'LIKE'},
                        {field: 'remark', title: '备注', operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 快速开奖按钮
            $(document).on('click', '.btn-quick-draw', function() {
                Fast.api.open('lottery/draw/quickdraw', '快速开奖', {
                    callback: function(data) {
                        table.bootstrapTable('refresh');
                    }
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        quickdraw: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 号码生成器
                $(document).on('click', '#generate-numbers', function() {
                    var numbers = [];
                    for(var i = 0; i < 7; i++) {
                        var num = Math.floor(Math.random() * 49) + 1;
                        numbers.push(num.toString().padStart ? num.toString().padStart(2, '0') : ('0' + num).slice(-2));
                    }
                    $('#c-numbers').val(numbers.join(','));
                });
                
                // 期数自动生成
                $(document).on('change', '#c-lottery_type', function() {
                    var lotteryType = $(this).val();
                    if(lotteryType) {
                        // 简化的期数生成逻辑
                        var currentTime = new Date();
                        var period = currentTime.getFullYear().toString().substr(2) + 
                                    (currentTime.getMonth() + 1).toString().padStart ? 
                                    (currentTime.getMonth() + 1).toString().padStart(2, '0') :
                                    ('0' + (currentTime.getMonth() + 1)).slice(-2) + 
                                    currentTime.getDate().toString().padStart ? 
                                    currentTime.getDate().toString().padStart(2, '0') :
                                    ('0' + currentTime.getDate()).slice(-2) + '001';
                        $('#c-period').val(period);
                    }
                });
                
                // 快速开奖页面的特殊处理
                if ($('#quickdraw-form').length > 0) {
                    // 号码输入监听
                    $(document).on('input', '#c-numbers', function() {
                        updatePreview();
                    });

                    // 随机生成号码
                    $(document).on('click', '#generate-random', function() {
                        var numbers = [];
                        for(var i = 0; i < 7; i++) {
                            var num = Math.floor(Math.random() * 49) + 1;
                            var numStr = num.toString().padStart ? num.toString().padStart(2, '0') : ('0' + num).slice(-2);
                            numbers.push(numStr);
                        }
                        $('#c-numbers').val(numbers.join(','));
                        updatePreview();
                    });

                    // 清空号码
                    $(document).on('click', '#clear-numbers', function() {
                        $('#c-numbers').val('');
                        updatePreview();
                    });

                    // 更新预览
                    function updatePreview() {
                        var preview = $('#numbers-preview');
                        var numbersStr = $('#c-numbers').val().trim();
                        var html = '';

                        if(numbersStr) {
                            var numbers = numbersStr.split(',');
                            for(var i = 0; i < numbers.length; i++) {
                                var num = parseInt(numbers[i].trim());
                                if(num >= 1 && num <= 49) {
                                    var numStr = num.toString().padStart ? num.toString().padStart(2, '0') : ('0' + num).slice(-2);
                                    html += '<span class="label label-primary" style="margin-right:5px; font-size:14px;">' + numStr + '</span>';
                                }
                            }
                        }

                        if(!html) {
                            html = '<span class="text-muted">请输入号码</span>';
                        }

                        preview.html(html);
                    }

                    // 表单验证
                    $(document).on('submit', '#quickdraw-form', function(e) {
                        var numbersStr = $('#c-numbers').val().trim();
                        if(!numbersStr) {
                            Toastr.error('请至少输入1个有效的号码');
                            e.preventDefault();
                            return false;
                        }

                        var numbers = numbersStr.split(',');
                        var validNumbers = 0;
                        for(var i = 0; i < numbers.length; i++) {
                            var num = parseInt(numbers[i].trim());
                            if(num >= 1 && num <= 49) {
                                validNumbers++;
                            }
                        }

                        if(validNumbers < 1) {
                            Toastr.error('请至少输入1个有效的号码（1-49之间）');
                            e.preventDefault();
                            return false;
                        }
                    });
                }
            }
        }
    };
    return Controller;
});
