<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$site.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 530px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* 顶部图片区域 */
        .hero-section {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 0;
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 未点击状态 - 灰色毛玻璃 */
        .tab-item:not(.active) {
            background: rgba(153, 153, 153, 0.8);
            color: white;
        }

        /* 点击状态颜色 - 毛玻璃效果 */
        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8); /* 绿色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8); /* 蓝色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8); /* 红色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8); /* 黑色毛玻璃 */
            color: white;
        }

        /* 悬停效果 */
        .tab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 开奖记录按钮 */
        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .record-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.4);
        }

        /* 号码球区域 */
        .balls-section {
            width: 100%;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .balls-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: nowrap;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 40px;
            flex-shrink: 0;
        }

        .ball-wrapper {
            position: relative;
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: calc(50% - 2px);
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .ball-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        .zodiac-element {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 加号分隔符 */
        .plus-separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
            margin: 0 2px;
            flex-shrink: 0;
        }

        /* 开奖时间区域 */
        .draw-time-section {
            width: 100%;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draw-time-text {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .draw-time-text .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 刷新按钮 */
        .refresh-btn {
            padding: 3px 10px;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
            outline: none;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
        }

        /* 红色区域 */
        .red-section {
            width: 530px;
            min-height: 210px;
            background: #ed0000;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0px 10px 0px 10px;
        }

        .red-content {
            display: flex;
            align-items: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .yellow-notice {
            color: #ffff00;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            line-height: 1.4;
        }

        .yellow-notice .small-text {
            font-size: 16px;
        }

        .arrow-icon {
            width: auto;
            height: auto;
            margin: 0 8px;
        }

        .arrow-left {
            transform: scaleX(-1);
        }

        /* 导航区域 */
        .nav-section {
            width: 530px;
            padding: 0;
            background: white;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
        }

        .nav-column {
            border-right: 1px solid #dee2e6;
        }

        .nav-column:last-child {
            border-right: none;
        }

        .nav-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 8px 6px;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: #f8f9fa;
        }

        .nav-item:last-child {
            border-bottom: none;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
            overflow: hidden;
        }

        .nav-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 第一列红色 */
        .nav-column:nth-child(1) .nav-text {
            color: #dc3545;
        }

        /* 第二列蓝色 */
        .nav-column:nth-child(2) .nav-text {
            color: #007bff;
        }

        /* 第三列绿色 */
        .nav-column:nth-child(3) .nav-text {
            color: #28a745;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 600;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 绿色长条 */
        .green-bar {
            width: 530px;
            height: 20px;
            background: #039e6d;
            display: flex;
            align-items: center;
            padding: 0 8px;
        }

        .green-bar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .green-bar-left {
            display: flex;
            align-items: center;
        }

        .green-bar-right {
            display: flex;
            align-items: center;
        }

        .home-icon {
            width: auto;
            height: 12px;
            margin-right: 3px;
        }

        .green-bar-clickable {
            color: white;
            font-size: 9px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin-right: 3px;
        }

        .green-bar-clickable:hover {
            text-decoration: underline;
        }

        .green-bar-text {
            color: white;
            font-size: 9px;
            font-weight: 600;
            margin-right: 3px;
        }

        .post-icon {
            width: auto;
            height: 12px;
            cursor: pointer;
        }

        .post-icon:hover {
            opacity: 0.8;
        }

        .user-info {
            color: white;
            font-size: 9px;
            font-weight: 600;
        }

        .user-link {
            color: white;
            font-size: 9px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin: 0 2px;
        }

        .user-link:hover {
            text-decoration: underline;
        }

        /* 登录表单 */
        .login-form {
            width: 530px;
            padding: 4px 8px;
            background: white;
            border-radius: 3px;
            margin-top: 1px;
        }

        .form-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 4px;
            flex-wrap: wrap;
        }

        .form-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-label {
            font-size: 9px;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
        }

        .form-input {
            padding: 2px 4px;
            border: 1px solid #ccc;
            border-radius: 2px;
            font-size: 9px;
            width: 50px;
            outline: none;
        }

        .form-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 1px rgba(0, 123, 255, 0.3);
        }

        .form-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
        }

        .form-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #f8f9fa 100%);
            transform: translateY(-1px);
        }

        .search-input {
            padding: 2px 4px;
            border: 1px solid #ccc;
            border-radius: 2px;
            font-size: 9px;
            width: 60px;
            outline: none;
        }

        .search-input:focus {
            border-color: #28a745;
            box-shadow: 0 0 1px rgba(40, 167, 69, 0.3);
        }

        .search-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
        }

        .search-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #f8f9fa 100%);
            transform: translateY(-1px);
        }

        /* 滚动公告 */
        .notice-scroll {
            width: 530px;
            height: auto;
            background: white;
            overflow: hidden;
            display: flex;
            align-items: center;
            margin-top: 0;
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0 0 3px 3px;
            padding: 8px 0;
        }

        .notice-content {
            color: #b91c1c;
            font-size: 16px;
            font-weight: 900;
            white-space: nowrap;
            animation: scroll-left 30s linear infinite;
            padding-left: 100%;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        /* 右下角浮动菜单 */
        .floating-menu {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .menu-item {
            width: 45px;
            height: 45px;
            background: #000000cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
        }

        .menu-item:hover {
            transform: scale(1.1);
        }

        /* 内容容器 */
        .content-container {
            width: 530px;
            min-height: 200px;
            background: white;
            border: 1px solid #28a745;
            margin-top: 0;
            padding: 10px;
        }

        /* 公告标题 */
        .announcement-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .announcement-icon {
            width: auto;
            height: 16px;
            margin-right: 6px;
        }

        .announcement-text {
            background: #ADEAEA;
            color: rgb(75, 0, 130);
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 1;
        }

        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        /* 弹窗容器 */
        .modal {
            width: 90%;
            max-width: 400px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: scale(0.7);
            transition: transform 0.3s ease;
        }

        .modal.show {
            transform: scale(1);
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            outline: none;
        }

        .form-group input:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        .modal-btn {
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-top: 10px;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }

        .success-message {
            color: #28a745;
            font-size: 12px;
            margin-top: 5px;
        }

        /* 充币弹窗保持原样 */
        .recharge-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 2000;
        }

        .recharge-modal {
            width: 100%;
            background: white;
            border-radius: 12px 12px 0 0;
            padding: 20px;
            text-align: center;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .recharge-modal.show {
            transform: translateY(0);
        }

        .close-recharge {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .recharge-qr {
            width: 200px;
            height: auto;
            margin: 10px 0 15px 0;
        }

        .recharge-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-top: 10px;
        }

        /* 根据分类ID设置不同颜色 - 全局应用 */
        .post-text.category-7 {
            color: red;
        }

        .post-text.category-8 {
            color: #ff0d86;
        }

        .post-text.category-9 {
            color: blue;
        }









        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }

            .red-section {
                width: 100%;
            }

            .nav-section {
                width: 100%;
            }

            .green-bar {
                width: 100%;
            }

            .login-form {
                width: 100%;
            }

            .notice-scroll {
                width: 100%;
            }

            .content-container {
                width: 100%;
            }

            /* 帖子列表样式 - 与公告格式一致 */
            .post-header {
                display: flex;
                align-items: center;
                padding: 8px 0px;
                margin-bottom: 5px;
            }

            .post-icon {
                width: 48px;
                height: 16px;
                margin-right: 8px;
                flex-shrink: 0;
                object-fit: cover;
            }

            .post-text {
                flex: 1;
                font-size: 14px;
                line-height: 1.4;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            /* 资料内容区域样式 */
            .ziliao-content {
                margin-top: 20px;
                width: 100%;
                max-width: 100%;
                line-height: 1.6;
                color: #333;
                font-size: 13px;
                word-break: break-all;
                overflow-wrap: anywhere;
                hyphens: auto;
                box-sizing: border-box;
                overflow: hidden;
            }

            .ziliao-content * {
                max-width: 100% !important;
                word-break: break-all !important;
                overflow-wrap: anywhere !important;
                box-sizing: border-box !important;
                font-size: 13px !important;
            }

            .ziliao-content img {
                max-width: 100% !important;
                height: auto !important;
            }

            .ziliao-content table {
                width: 100% !important;
                table-layout: fixed !important;
            }

            .ziliao-content pre, .ziliao-content code {
                white-space: pre-wrap !important;
                word-break: break-all !important;
            }

            /* 只针对电脑端防止溢出 */
            @media (min-width: 531px) {
                .ziliao-content {
                    max-width: 510px !important;
                }

                .ziliao-content, .ziliao-content * {
                    font-size: 12px !important;
                }
            }

        }

        /* 闪烁动画 */
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 顶部图片区域 -->
        <section class="hero-section">
            <img border="0" src="{$site.top_images}" width="100%" height="180">
        </section>

        <!-- 标签区域 -->
        <section class="tabs-section">
            <div class="tabs-container">
                <button class="tab-item tab-1 active" onclick="selectTab(1)">{$site.sicai}</button>
                <button class="tab-item tab-2" onclick="selectTab(2)">新澳</button>
                <button class="tab-item tab-3" onclick="selectTab(3)">香港</button>
                <button class="tab-item tab-4" onclick="selectTab(4)">老澳</button>
            </div>
        </section>

        <!-- 开奖信息区域 -->
        <section class="lottery-info">
            <div class="lottery-text" id="lotteryText">
                正在加载开奖数据...
            </div>
            <button class="record-btn" onclick="showRecord()">开奖记录</button>
        </section>

        <!-- 号码球区域 -->
        <section class="balls-section">
            <div class="balls-container" id="ballsContainer">
                <!-- 7个号码球将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 开奖时间区域 -->
        <section class="draw-time-section">
            <div class="draw-time-text" id="drawTimeText">
                第<span class="period-number">201</span>期开奖时间07月28日 周一22点32分
            </div>
            <button class="refresh-btn" onclick="refreshData()">刷新</button>
        </section>
    </div>

    <!-- 红色区域 -->
    <div class="red-section">
        <div class="red-content">
            <!--<img src="/images/shuangjiantou.gif" alt="箭头" class="arrow-icon arrow-left">
            <span>六合宝典◆发帖有奖</span>
            <img src="/images/shuangjiantou.gif" alt="箭头" class="arrow-icon">-->
        </div>
        <div class="yellow-notice">
            {$site.noito_msg}
        </div>
    </div>

    <!-- 导航区域 -->
    <div class="nav-section">
        <div class="nav-grid" id="navigationGrid">
            <!-- 导航内容将通过JavaScript动态加载 -->
            <div class="nav-loading" style="text-align: center; padding: 20px; color: #666;">
                正在加载导航...
            </div>
        </div>
    </div>

    <!-- 绿色长条 -->
    <div class="green-bar">
        <div class="green-bar-content">
            <div class="green-bar-left">
                <img src="/images/home.gif" alt="首页" class="home-icon">
                <a href="#" class="green-bar-clickable" onclick="contactAdmin()">『澳门内部最准资料』</a>
                <span class="green-bar-text">☎ 论坛管理員：</span>
                <img src="/images/post.gif" alt="发帖" class="post-icon" onclick="postMessage()">
            </div>
            <div class="green-bar-right" id="userStatusArea">
                <span class="user-info" id="userStatusText">您当前是游客：</span>
                <a href="#" class="user-link" id="loginLink" onclick="showLogin()">登录</a>
                <span class="user-info" id="divider">|</span>
                <a href="#" class="user-link" id="registerLink" onclick="showRegister()">注册</a>
            </div>
        </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form" id="loginFormArea">
        <div class="form-row">
            <div class="form-left">
                <label class="form-label">用户名:</label>
                <input type="text" id="homeLoginAccount" class="form-input" placeholder="用户名">
                <label class="form-label">密码:</label>
                <input type="password" id="homeLoginPassword" class="form-input" placeholder="密码">
                <button class="form-btn btn-login" onclick="handleHomeLogin()">登录</button>
                <button class="form-btn btn-register" onclick="handleRegister()">注册</button>
            </div>
            <div class="form-right">
                <input type="text" class="search-input" placeholder="搜索">
                <button class="search-btn" onclick="handleSearch()">搜索</button>
            </div>
        </div>
    </div>

    <!-- 滚动公告 -->
    <div class="notice-scroll">
        <div class="notice-content">
            {$site.msg}
        </div>
    </div>

    <!-- 内容容器 -->
    <div class="content-container">
        <!-- 公告标题 -->
        <div class="announcement-header">
            <img src="{$site.tu1}" alt="公告图标" class="announcement-icon">
            <div class="announcement-text">
                {$site.notio1}
            </div>
        </div>

        <!-- 第二条公告 -->
        <div class="announcement-header">
            <img src="{$site.tu1}" alt="公告图标" class="announcement-icon">
            <div class="announcement-text">
                {$site.notio2}
            </div>
        </div>
        <!-- 帖子列表区域 -->
        <div id="postsList">
            <!-- 帖子列表将通过JavaScript动态加载 -->
        </div>

        <!-- 资料内容区域 -->
        <div class="ziliao-content">
            {$site.ziliao}
        </div>
    </div>

    <!-- 充币弹窗遮罩 -->
    <div class="recharge-overlay" id="rechargeOverlay">
        <div class="recharge-modal">
            <span class="close-recharge" onclick="closeRecharge()">&times;</span>
            <img src="{$site.kefu}" alt="充币二维码" class="recharge-qr">
            <div class="recharge-title">扫码联系客服</div>
        </div>
    </div>

    <!-- 右下角浮动菜单 -->
    <div class="floating-menu">
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('注册')">注册</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('充币')">充币</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('优惠')">优惠</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('介绍')">介绍</a>
    </div>

    <script>
        function selectTab(tabNumber) {
            // 移除所有标签的active类
            const allTabs = document.querySelectorAll('.tab-item');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 给点击的标签添加active类
            const selectedTab = document.querySelector(`.tab-${tabNumber}`);
            selectedTab.classList.add('active');

            // 清除私彩直播状态和定时器
            if (window.sicaiLiveDrawingActive) {
                window.sicaiLiveDrawingActive = false;
            }
            if (typeof sicaiDrawingTimers !== 'undefined') {
                sicaiDrawingTimers.forEach(timer => clearTimeout(timer));
                sicaiDrawingTimers = [];
            }

            // 更新当前激活标签并重新开始轮询
            currentActiveTab = tabNumber;
            stopPolling(); // 先停止之前的轮询

            // 根据不同标签获取对应的真实数据
            if (tabNumber === 1) {
                // 私彩使用手动开奖数据
                isTabSwitch = true; // 标记为标签切换
                fetchManualData('sicai').then(data => {
                    if (data) {
                        updateSicaiDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">---</span>期开奖结果: <span style="color: #999;">暂无数据</span>`;
                        updateBalls(tabNumber);
                        updateDrawTime(tabNumber);
                    }
                });
            } else if (tabNumber === 2) {
                // 新澳门 - 立即获取最新数据
                fetchAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `新澳门 第<span class="period-number">--</span>期开奖结果:`;
                    }
                });
            } else if (tabNumber === 3) {
                // 香港 - 立即获取最新数据
                fetchXgLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `香港 第<span class="period-number">--</span>期开奖结果:`;
                    }
                });
            } else if (tabNumber === 4) {
                // 老澳门 - 立即获取最新数据
                fetchOldAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                    } else {
                        // 获取失败时的默认显示
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `老澳门 第<span class="period-number">--</span>期开奖结果:`;
                    }
                });
            }

            // 开始轮询当前标签的数据
            startPolling();
        }

        function updateBalls(tabNumber) {
            const ballsContainer = document.getElementById('ballsContainer');
            const ballsData = {
                1: [
                    { number: '08', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '15', zodiac: '兔', element: '木', color: 'green' },
                    { number: '23', zodiac: '猪', element: '火', color: 'red' },
                    { number: '31', zodiac: '马', element: '火', color: 'red' },
                    { number: '42', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '07', zodiac: '牛', element: '土', color: 'green' },
                    { number: '19', zodiac: '羊', element: '土', color: 'red' }
                ],
                3: [
                    { number: '01', zodiac: '猪', element: '火', color: 'red' },
                    { number: '14', zodiac: '虎', element: '木', color: 'green' },
                    { number: '27', zodiac: '兔', element: '木', color: 'green' },
                    { number: '33', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '45', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '09', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '22', zodiac: '虎', element: '木', color: 'green' }
                ],
                4: [
                    { number: '05', zodiac: '马', element: '火', color: 'red' },
                    { number: '16', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '28', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '37', zodiac: '猪', element: '火', color: 'red' },
                    { number: '44', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '11', zodiac: '猪', element: '火', color: 'red' },
                    { number: '29', zodiac: '马', element: '火', color: 'red' }
                ]
            };

            const balls = ballsData[tabNumber];
            ballsContainer.innerHTML = '';

            balls.forEach((ball, index) => {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ball.color}.png" alt="${ball.color} ball" class="ball-image">
                        <div class="ball-number">${ball.number}</div>
                    </div>
                    <div class="ball-info">${ball.zodiac}/${ball.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                // 在第6个球后添加加号分隔符
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });
        }

        function updateDrawTime(tabNumber) {
            const drawTimeText = document.getElementById('drawTimeText');
            const drawTimes = {
                1: { period: '200', time: '开奖时间07月28日 周一21点32分' },
                3: { period: '089', time: '开奖时间07月28日 周一19点45分' },
                4: { period: '234', time: '开奖时间07月28日 周一22点10分' }
            };

            const drawTime = drawTimes[tabNumber];
            if (drawTime) {
                drawTimeText.innerHTML = `第<span class="period-number">${drawTime.period}</span>期${drawTime.time}`;
            }
        }

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时停止轮询
                stopPolling();

            } else {
                // 页面显示时恢复轮询
                startPolling();

            }
        });

        // 获取彩种名称
        const sicaiName = '{$site.sicai}';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载私彩手动开奖数据
            fetchManualData('sicai').then(data => {
                if (data) {
                    updateSicaiDisplay(data);
                } else {
                    // 获取失败时的默认显示
                    const lotteryText = document.getElementById('lotteryText');
                    lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">---</span>期开奖结果: <span style="color: #999;">暂无数据</span>`;
                    updateBalls(1); // 默认显示私彩的球
                    updateDrawTime(1); // 默认显示私彩的开奖时间
                }
            });

            loadNavigation(); // 加载导航数据
            checkUserLoginStatus(); // 检查用户登录状态

            // 开始轮询
            startPolling();

            // 不预加载数据，只有点击标签时才加载
        });

        // 检查用户登录状态
        function checkUserLoginStatus() {
            const userToken = localStorage.getItem('userToken');
            const userInfo = localStorage.getItem('userInfo');

            if (userToken && userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    // 更新状态显示
                    document.getElementById('userStatusText').textContent = `欢迎，${user.username || user.nickname || '用户'}：`;

                    // 更新链接
                    const statusArea = document.getElementById('userStatusArea');
                    statusArea.innerHTML = `
                        <span class="user-info" id="userStatusText">欢迎，${user.username || user.nickname || '用户'}：</span>
                        <a href="/index/index/profile" class="user-link">控制中心</a>
                        <span class="user-info">|</span>
                        <a href="#" class="user-link" onclick="logout()">退出</a>
                    `;

                    // 隐藏登录表单
                    document.getElementById('loginFormArea').style.display = 'none';
                } catch (error) {
                    console.error('用户信息解析失败:', error);
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('userInfo');
                }
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('userToken');
            localStorage.removeItem('userInfo');
            location.reload(); // 刷新页面
        }

        // 首页登录处理
        async function handleHomeLogin() {
            const account = document.getElementById('homeLoginAccount').value.trim();
            const password = document.getElementById('homeLoginPassword').value.trim();

            if (!account || !password) {
                alert('请输入用户名和密码');
                return;
            }

            try {
                const response = await fetch('/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        account: account,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.code === 1) {
                    // 登录成功
                    if (result.data && result.data.userinfo) {
                        localStorage.setItem('userToken', result.data.userinfo.token);
                        localStorage.setItem('userInfo', JSON.stringify(result.data.userinfo));
                    }

                    alert('登录成功！');
                    checkUserLoginStatus(); // 更新登录状态显示
                } else {
                    alert(result.msg || '登录失败，请检查用户名和密码');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 加载导航数据
        async function loadNavigation() {
            try {
                const response = await fetch('/api/navigation/index');
                const result = await response.json();

                if (result.code === 1 && result.data && result.data.length > 0) {
                    renderNavigation(result.data);
                } else {
                    showNavigationError('暂无导航数据');
                }
            } catch (error) {
                console.error('加载导航失败:', error);
                showNavigationError('导航加载失败');
            }
        }

        // 渲染导航
        function renderNavigation(navigationData) {
            const navigationGrid = document.getElementById('navigationGrid');

            // 将导航数据按3列分组
            const columns = [[], [], []];
            navigationData.forEach((nav, index) => {
                columns[index % 3].push(nav);
            });

            // 生成HTML
            let html = '';
            columns.forEach((column, columnIndex) => {
                html += '<div class="nav-column">';
                column.forEach(nav => {
                    const iconHtml = nav.image ?
                        `<img src="${nav.image}" alt="${nav.title}">` :
                        `<span>${nav.title.charAt(0)}</span>`;

                    html += `
                        <a href="${nav.url}" class="nav-item" target="${nav.target}" onclick="navigateTo('${nav.title}')">
                            <div class="nav-icon">${iconHtml}</div>
                            <div class="nav-text">${nav.title}</div>
                        </a>
                    `;
                });
                html += '</div>';
            });

            navigationGrid.innerHTML = html;
        }

        // 显示导航错误信息
        function showNavigationError(message) {
            const navigationGrid = document.getElementById('navigationGrid');
            navigationGrid.innerHTML = `
                <div class="nav-loading" style="text-align: center; padding: 20px; color: #dc3545;">
                    ${message}
                </div>
            `;
        }

        // 开奖数据缓存
        let amLotteryData = null;  // 新澳门
        let xgLotteryData = null;  // 香港
        let oldAmLotteryData = null;  // 老澳门

        // 轮询相关变量
        let pollingInterval = null;  // 轮询定时器
        let currentActiveTab = 1;    // 当前激活的标签

        // 生肖、五行、波色对应关系
        function getNumberAttributes(num) {
            const number = parseInt(num);

            // 生肖对应关系
            const zodiacMap = {
                6: '鼠', 18: '鼠', 30: '鼠', 42: '鼠',
                5: '牛', 17: '牛', 29: '牛', 41: '牛',
                4: '虎', 16: '虎', 28: '虎', 40: '虎',
                3: '兔', 15: '兔', 27: '兔', 39: '兔',
                2: '龙', 14: '龙', 26: '龙', 38: '龙',
                1: '蛇', 13: '蛇', 25: '蛇', 37: '蛇', 49: '蛇',
                12: '马', 24: '马', 36: '马', 48: '马',
                11: '羊', 23: '羊', 35: '羊', 47: '羊',
                10: '猴', 22: '猴', 34: '猴', 46: '猴',
                9: '鸡', 21: '鸡', 33: '鸡', 45: '鸡',
                8: '狗', 20: '狗', 32: '狗', 44: '狗',
                7: '猪', 19: '猪', 31: '猪', 43: '猪'
            };

            // 五行对应关系
            const elementMap = {
                3: '金', 4: '金', 11: '金', 12: '金', 25: '金', 26: '金', 33: '金', 34: '金', 41: '金', 42: '金',
                7: '木', 8: '木', 15: '木', 16: '木', 23: '木', 24: '木', 37: '木', 38: '木', 45: '木', 46: '木',
                13: '水', 14: '水', 21: '水', 22: '水', 29: '水', 30: '水', 43: '水', 44: '水',
                1: '火', 2: '火', 9: '火', 10: '火', 17: '火', 18: '火', 31: '火', 32: '火', 39: '火', 40: '火', 47: '火', 48: '火',
                5: '土', 6: '土', 19: '土', 20: '土', 27: '土', 28: '土', 35: '土', 36: '土', 49: '土'
            };

            // 波色对应关系
            const colorMap = {
                1: 'red', 2: 'red', 7: 'red', 8: 'red', 12: 'red', 13: 'red', 18: 'red', 19: 'red',
                23: 'red', 24: 'red', 29: 'red', 30: 'red', 34: 'red', 35: 'red', 40: 'red', 45: 'red', 46: 'red',

                3: 'blue', 4: 'blue', 9: 'blue', 10: 'blue', 14: 'blue', 15: 'blue', 20: 'blue', 25: 'blue',
                26: 'blue', 31: 'blue', 36: 'blue', 37: 'blue', 41: 'blue', 42: 'blue', 47: 'blue', 48: 'blue',

                5: 'green', 6: 'green', 11: 'green', 16: 'green', 17: 'green', 21: 'green', 22: 'green', 27: 'green',
                28: 'green', 32: 'green', 33: 'green', 38: 'green', 39: 'green', 43: 'green', 44: 'green', 49: 'green'
            };

            return {
                number: num.padStart(2, '0'),
                zodiac: zodiacMap[number] || '未知',
                element: elementMap[number] || '未知',
                color: colorMap[number] || 'blue'
            };
        }

        // 获取新澳门开奖数据
        async function fetchAmLotteryData() {
            try {
                const response = await fetch('/api/lottery/getAmData');
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    amLotteryData = result.data;

                    return amLotteryData;
                } else {
                    console.error('获取新澳门数据失败:', result.msg || '未知错误');
                }
            } catch (error) {
                console.error('获取新澳门数据失败:', error);
            }
            return null;
        }

        // 获取香港开奖数据
        async function fetchXgLotteryData() {
            try {
                const response = await fetch('/api/lottery/getXgData');
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    xgLotteryData = result.data;

                    return xgLotteryData;
                } else {
                    console.error('获取香港数据失败:', result.msg || '未知错误');
                }
            } catch (error) {
                console.error('获取香港数据失败:', error);
            }
            return null;
        }

        // 获取老澳门开奖数据
        async function fetchOldAmLotteryData() {
            try {
                const response = await fetch('/api/lottery/get48AmData');
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    oldAmLotteryData = result.data;

                    return oldAmLotteryData;
                } else {
                    console.error('获取老澳门数据失败:', result.msg || '未知错误');
                }
            } catch (error) {
                console.error('获取老澳门数据失败:', error);
            }
            return null;
        }

        // 获取手动开奖数据（仅用于私彩）
        async function fetchManualData(lotteryType) {
            try {
                const response = await fetch(`/api/lottery/getManualData?type=${lotteryType}`);
                const result = await response.json();

                if (result.code === 1 && result.data) {

                    return result.data;
                } else {
                    console.error(`获取${lotteryType}手动开奖数据失败:`, result.msg || '未知错误');
                }
            } catch (error) {
                console.error(`获取${lotteryType}手动开奖数据失败:`, error);
            }
            return null;
        }

        // 存储上次开奖数据，用于比较
        let lastSicaiData = null;
        let isInitialLoad = true; // 标记是否是初始加载
        let isTabSwitch = false; // 标记是否是标签切换

        // 私彩专用显示函数（带直播效果）
        function updateSicaiDisplay(data) {
            if (!data) return;

            // 如果是初始加载或标签切换，直接显示最新号码，不启动直播效果
            if (isInitialLoad || isTabSwitch) {
                isInitialLoad = false;
                isTabSwitch = false;
                lastSicaiData = data;

                // 初始加载或标签切换使用专门的显示函数
                updateSicaiStaticDisplay(data);
                return;
            }

            // 轮询时才开始对比期数
            const currentDataKey = `${data.periods}_${data.num}`;
            const lastDataKey = lastSicaiData ? `${lastSicaiData.periods}_${lastSicaiData.num}` : null;

            if (currentDataKey === lastDataKey) {
                return;
            }

            // 检查是否是同一期的新号码（增量开奖）
            const currentNumbers = data.num.split(',').map(n => n.trim());
            const lastNumbers = lastSicaiData ? lastSicaiData.num.split(',').map(n => n.trim()) : [];

            if (lastSicaiData && data.periods === lastSicaiData.periods) {
                // 同一期，检查是否有新号码
                if (currentNumbers.length > lastNumbers.length) {
                    // 增量开奖：只开出新增的号码
                    const newNumbers = currentNumbers.slice(lastNumbers.length);
                    addNewSicaiNumbers(data, newNumbers, lastNumbers.length);

                    // 保存当前数据
                    lastSicaiData = data;
                    return;
                }
            }

            // 新期数或完全不同的数据，重新开奖
            lastSicaiData = data;

            // 轮询时有新数据才使用直播效果
            startSicaiLiveDrawing(data, currentNumbers);
        }

        // 存储开奖定时器，用于清除之前的开奖
        let sicaiDrawingTimers = [];

        // 私彩静态显示函数（用于初始加载）
        function updateSicaiStaticDisplay(data) {
            if (!data) return;

            // 更新期数和标题（使用私彩配置名称）
            const lotteryText = document.getElementById('lotteryText');
            lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">${data.periods}</span>期开奖结果:`;

            // 更新号码球
            const ballsContainer = document.getElementById('ballsContainer');
            const numbers = data.num.split(',');
            ballsContainer.innerHTML = '';

            numbers.forEach((num, index) => {
                const ballData = getNumberAttributes(num.trim());

                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ballData.color}.png" alt="${ballData.color} ball" class="ball-image">
                        <div class="ball-number">${ballData.number}</div>
                    </div>
                    <div class="ball-info">
                        <div class="zodiac-element">${ballData.zodiac}/${ballData.element}</div>
                    </div>
                `;
                ballsContainer.appendChild(ballItem);

                // 只在第6个球后面添加加号分隔符（前6个是正码，第7个是特码）
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });

            // 更新下一期开奖时间
            const drawTimeText = document.getElementById('drawTimeText');
            if (data.nextperiods && data.nexttime) {
                const nextTime = new Date(data.nexttime);
                const nextTimeStr = `${nextTime.getMonth() + 1}月${nextTime.getDate()}日 ${['周日','周一','周二','周三','周四','周五','周六'][nextTime.getDay()]}${nextTime.getHours()}点${nextTime.getMinutes().toString().padStart(2, '0')}分`;
                drawTimeText.innerHTML = `第<span class="period-number">${data.nextperiods}</span>期开奖时间${nextTimeStr}`;
            }
        }

        // 增量开奖：只开出新增的号码，不清除已开出的号码
        function addNewSicaiNumbers(data, newNumbers, startIndex) {
            const ballsContainer = document.getElementById('ballsContainer');
            const lotteryText = document.getElementById('lotteryText');

            // 设置直播状态标记
            window.sicaiLiveDrawingActive = true;



            // 逐个开出新号码
            newNumbers.forEach((number, index) => {
                const timer = setTimeout(() => {
                    const attributes = getNumberAttributes(number);
                    const ballItems = ballsContainer.querySelectorAll('.ball-item');
                    const targetBall = ballItems[startIndex + index];

                    if (targetBall) {
                        // 替换对应位置的黑球
                        targetBall.innerHTML = `
                            <div class="ball-wrapper">
                                <img src="/images/ball-${attributes.color}.png" alt="${attributes.color} ball" class="ball-image">
                                <div class="ball-number">${attributes.number}</div>
                            </div>
                            <div class="ball-info">
                                <div class="zodiac-element">${attributes.zodiac}/${attributes.element}</div>
                            </div>
                        `;

                        // 开出的号码保持稳定显示
                        targetBall.style.animation = 'none';
                        targetBall.style.opacity = '1';
                    }

                    // 更新标题
                    const totalOpened = startIndex + index + 1;
                    lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">${data.periods}</span>期开奖结果: <span style="color: #28a745;">已开出 ${totalOpened} 个号码</span>`;

                    // 如果是最后一个新号码，清除直播状态
                    if (index === newNumbers.length - 1) {
                        setTimeout(() => {
                            lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">${data.periods}</span>期开奖结果:`;
                            window.sicaiLiveDrawingActive = false;
                        }, 2000);
                    }

                }, 2000 * (index + 1)); // 每2秒开出一个新号码

                sicaiDrawingTimers.push(timer);
            });

            // 更新下一期开奖时间
            const drawTimeText = document.getElementById('drawTimeText');
            if (data.nextperiods && data.nexttime) {
                const nextTime = new Date(data.nexttime);
                const nextTimeStr = `${nextTime.getMonth() + 1}月${nextTime.getDate()}日 ${['周日','周一','周二','周三','周四','周五','周六'][nextTime.getDay()]}${nextTime.getHours()}点${nextTime.getMinutes().toString().padStart(2, '0')}分`;
                drawTimeText.innerHTML = `第<span class="period-number">${data.nextperiods}</span>期开奖时间${nextTimeStr}`;
            }
        }

        // 私彩直播开奖效果（支持多个号码逐个开奖）
        function startSicaiLiveDrawing(data, numbers) {
            const ballsContainer = document.getElementById('ballsContainer');
            const lotteryText = document.getElementById('lotteryText');

            // 清除之前的开奖定时器
            sicaiDrawingTimers.forEach(timer => clearTimeout(timer));
            sicaiDrawingTimers = [];

            // 设置直播状态标记，防止轮询干扰
            window.sicaiLiveDrawingActive = true;

            // 清空容器
            ballsContainer.innerHTML = '';

            // 显示7个灰色球和占位符
            for (let i = 0; i < 7; i++) {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/blak.png" alt="待开奖" class="ball-image">
                        <div class="ball-number">?</div>
                    </div>
                    <div class="ball-info">
                        <div class="zodiac-element">/ / /</div>
                    </div>
                `;
                ballsContainer.appendChild(ballItem);

                // 在第6个球后面添加加号分隔符
                if (i === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            }

            // 显示"正在直播开奖中"
            lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">${data.periods}</span>期 <span style="color: #ff6b6b; font-weight: bold; animation: blink 1s infinite;">正在直播开奖中...</span>`;

            // 逐个开奖，每个号码间隔3秒
            numbers.forEach((number, index) => {
                const timer = setTimeout(() => {
                    const attributes = getNumberAttributes(number);
                    const ballItems = ballsContainer.querySelectorAll('.ball-item');
                    const targetBall = ballItems[index];

                    if (targetBall) {
                        // 替换对应位置的黑球
                        targetBall.innerHTML = `
                            <div class="ball-wrapper">
                                <img src="/images/ball-${attributes.color}.png" alt="${attributes.color} ball" class="ball-image">
                                <div class="ball-number">${attributes.number}</div>
                            </div>
                            <div class="ball-info">
                                <div class="zodiac-element">${attributes.zodiac}/${attributes.element}</div>
                            </div>
                        `;

                        // 开出的号码保持稳定显示，不闪烁
                        targetBall.style.animation = 'none';
                        targetBall.style.opacity = '1';
                    }

                    // 更新标题显示进度
                    const openedCount = index + 1;
                    const totalCount = numbers.length;

                    if (openedCount < totalCount) {
                        // 还有更多号码
                        lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">${data.periods}</span>期开奖结果: <span style="color: #28a745;">已开出 ${openedCount}/${totalCount}</span> <span style="color: #6c757d;">等待更多号码...</span>`;
                    } else {
                        // 所有号码开完
                        lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">${data.periods}</span>期开奖结果:`;
                        // 清除直播状态标记，允许轮询恢复
                        window.sicaiLiveDrawingActive = false;
                    }

                }, 3000 + (index * 2000)); // 第一个号码3秒后开出，后续每2秒开出一个

                // 保存定时器，用于清除
                sicaiDrawingTimers.push(timer);
            });

            // 更新下一期开奖时间
            const drawTimeText = document.getElementById('drawTimeText');
            if (data.nextperiods && data.nexttime) {
                const nextTime = new Date(data.nexttime);
                const nextTimeStr = `${nextTime.getMonth() + 1}月${nextTime.getDate()}日 ${['周日','周一','周二','周三','周四','周五','周六'][nextTime.getDay()]}${nextTime.getHours()}点${nextTime.getMinutes().toString().padStart(2, '0')}分`;
                drawTimeText.innerHTML = `第<span class="period-number">${data.nextperiods}</span>期开奖时间${nextTimeStr}`;
            }
        }

        // 通用显示函数
        function updateLotteryDisplay(data) {
            if (!data) return;

            // 更新期数和标题（使用API返回的标题）
            const lotteryText = document.getElementById('lotteryText');
            lotteryText.innerHTML = `${data.title} 第<span class="period-number">${data.periods}</span>期开奖结果:`;

            // 更新号码球
            const ballsContainer = document.getElementById('ballsContainer');
            const numbers = data.num.split(',');
            ballsContainer.innerHTML = '';

            numbers.forEach((num, index) => {
                const ballData = getNumberAttributes(num.trim());

                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ballData.color}.png" alt="${ballData.color} ball" class="ball-image">
                        <div class="ball-number">${ballData.number}</div>
                    </div>
                    <div class="ball-info">
                        <div class="zodiac-element">${ballData.zodiac}/${ballData.element}</div>
                    </div>
                `;
                ballsContainer.appendChild(ballItem);

                // 只在第6个球后面添加加号分隔符（前6个是正码，第7个是特码）
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });

            // 更新下期开奖时间
            const drawTimeText = document.getElementById('drawTimeText');

            if (data.nextperiods && data.nexttime) {
                const nextTime = new Date(data.nexttime);
                const nextTimeStr = `${nextTime.getMonth() + 1}月${nextTime.getDate()}日 ${['周日','周一','周二','周三','周四','周五','周六'][nextTime.getDay()]}${nextTime.getHours()}点${nextTime.getMinutes().toString().padStart(2, '0')}分`;
                drawTimeText.innerHTML = `第<span class="period-number">${data.nextperiods}</span>期开奖时间${nextTimeStr}`;
            }
        }

        function refreshData() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            if (tabNumber === 1) {
                // 私彩使用手动开奖数据
                fetchManualData('sicai').then(data => {
                    if (data) {
                        updateSicaiDisplay(data);
                        alert('私彩数据已刷新');
                    } else {
                        const lotteryText = document.getElementById('lotteryText');
                        lotteryText.innerHTML = `${sicaiName} 第<span class="period-number">---</span>期开奖结果: <span style="color: #999;">暂无数据</span>`;
                        alert('私彩数据刷新失败');
                    }
                });
            } else if (tabNumber === 2) {
                // 新澳门
                fetchAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                        alert('新澳门数据已刷新');
                    } else {
                        alert('新澳门数据刷新失败');
                    }
                });
            } else if (tabNumber === 3) {
                // 香港
                fetchXgLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                        alert('香港数据已刷新');
                    } else {
                        alert('香港数据刷新失败');
                    }
                });
            } else if (tabNumber === 4) {
                // 老澳门
                fetchOldAmLotteryData().then(data => {
                    if (data) {
                        updateLotteryDisplay(data);
                        alert('老澳门数据已刷新');
                    } else {
                        alert('老澳门数据刷新失败');
                    }
                });
            }
        }

        // 开始轮询当前标签的数据
        function startPolling() {
            // 清除之前的轮询
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            // 所有标签都进行轮询

            // 每3秒轮询一次，确保实时性
            pollingInterval = setInterval(() => {


                if (currentActiveTab === 1) {
                    // 私彩 - 手动开奖数据
                    // 如果正在直播开奖，不进行轮询更新
                    if (window.sicaiLiveDrawingActive) {
                        return;
                    }

                    fetchManualData('sicai').then(data => {
                        if (data) {
                            updateSicaiDisplay(data);
                        }
                    });
                } else if (currentActiveTab === 2) {
                    // 新澳门
                    fetchAmLotteryData().then(data => {
                        if (data) {
                            updateLotteryDisplay(data);

                        }
                    });
                } else if (currentActiveTab === 3) {
                    // 香港
                    fetchXgLotteryData().then(data => {
                        if (data) {
                            updateLotteryDisplay(data);

                        }
                    });
                } else if (currentActiveTab === 4) {
                    // 老澳门
                    fetchOldAmLotteryData().then(data => {
                        if (data) {
                            updateLotteryDisplay(data);

                        }
                    });
                }
            }, 3000); // 3秒轮询一次，确保实时性
        }

        // 停止轮询
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;

            }
        }

        function showRecord() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            let url = '';
            if (tabNumber === 1) {
                // 私彩跳转到专门的记录页面
                window.location.href = '/index/index/sicai_record';
                return;
            } else if (tabNumber === 2) {
                // 新澳门
                url = 'https://zhibo.77kj.vip/kj/index.html?am';
            } else if (tabNumber === 3) {
                // 香港
                url = 'https://zhibo.77kj.vip/kj/index.html?xg';
            } else if (tabNumber === 4) {
                // 老澳门
                url = 'https://zhibo.77kj.vip/kj/index.html?48am';
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        function navigateTo(forumName) {
            // 这个函数现在主要用于统计点击，实际跳转由链接的href处理

            // 可以在这里添加统计代码
        }

        function contactAdmin() {
            alert('澳门内部最准资料');
        }

        function postMessage() {
            alert('发帖功能');
        }

        function showLogin() {
            window.location.href = '/index/index/login';
        }

        function showRegister() {
            // 检查是否已登录
            const userToken = localStorage.getItem('userToken');
            if (userToken) {
                alert('您已登录，无需重复登录！');
                return;
            }
            window.location.href = '/index/index/register';
        }

        function handleLogin() {
            window.location.href = '/index/index/login';
        }

        function handleRegister() {
            // 检查是否已登录
            const userToken = localStorage.getItem('userToken');
            if (userToken) {
                alert('您已登录，无需重复登录！');
                return;
            }
            window.location.href = '/index/index/register';
        }

        function showProfile() {
            window.location.href = '/index/index/profile';
        }

        function handleSearch() {
            const searchText = document.querySelector('.search-input').value;
            if (searchText) {
                alert('搜索：' + searchText);
            } else {
                alert('请输入搜索内容');
            }
        }

        function handleMenuClick(action) {
            if (action === '注册') {
                // 检查是否已登录
                const userToken = localStorage.getItem('userToken');
                if (userToken) {
                    alert('您已登录，无需重复登录！');
                    return;
                }
                window.location.href = '/index/index/register';
            } else if (action === '充币') {
                openRecharge();
            } else if (action === '优惠') {
                window.location.href = '/index/index/promotion';
            } else if (action === '介绍') {
                window.location.href = '/index/index/about';
            } else {
                alert(action + '功能');
            }
        }

        function openRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            overlay.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // 防止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function closeRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                overlay.style.display = 'none';
                // 恢复页面滚动
                document.body.style.overflow = '';
            }, 300);
        }

        // 点击空白区域关闭弹窗
        // 加载帖子列表
        function loadPosts() {
            const postsList = document.getElementById('postsList');

            fetch('/api/posts?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data && data.data.list) {
                        renderPosts(data.data.list);
                    } else {
                        // 如果没有帖子数据，不显示任何内容
                        postsList.innerHTML = '';
                    }
                })
                .catch(error => {
                    console.error('加载帖子失败:', error);
                    // 加载失败也不显示错误信息，保持空白
                    postsList.innerHTML = '';
                });
        }

        // 渲染帖子列表
        function renderPosts(posts) {
            const postsList = document.getElementById('postsList');

            if (posts.length === 0) {
                postsList.innerHTML = '';
                return;
            }

            let html = '';
            posts.forEach(post => {
                const iconUrl = post.icon_url;
                const categoryId = post.category.id;
                const categoryClass = `category-${categoryId}`;

                html += `
                    <div class="post-header" onclick="viewPost(${post.id})">
                        <img src="${iconUrl}" alt="帖子图标" class="post-icon">
                        <div class="post-text ${categoryClass}">
                            ${post.title}
                        </div>
                    </div>
                `;
            });

            postsList.innerHTML = html;
        }

        // 查看帖子详情
        function viewPost(postId) {
            window.location.href = `/index/index/post_detail?id=${postId}`;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.getElementById('rechargeOverlay');
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeRecharge();
                }
            });

            // 加载帖子列表
            loadPosts();
        });
    </script>
</body>
</html>
