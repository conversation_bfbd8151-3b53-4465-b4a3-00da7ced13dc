<?php

namespace app\common\model;

use think\Model;

/**
 * 手动开奖模型
 */
class LotteryDraw extends Model
{
    protected $name = 'lottery_draws';
    
    // 设置当前模型对应的完整数据表名称
    protected $table = 'fa_lottery_draws';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义字段类型
    protected $type = [
        'draw_time' => 'datetime',
        'next_draw_time' => 'datetime',
        'createtime' => 'timestamp',
        'updatetime' => 'timestamp',
    ];
    
    // 彩种类型
    const LOTTERY_TYPES = [
        'sicai' => '私彩',
        'xinao' => '新澳门',
        'xianggang' => '香港',
        'laoao' => '老澳门'
    ];
    
    // 状态
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 获取彩种名称
     */
    public function getLotteryTypeTextAttr($value, $data)
    {
        return self::LOTTERY_TYPES[$data['lottery_type']] ?? $data['lottery_type'];
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用'
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 获取号码数组
     */
    public function getNumbersArrayAttr($value, $data)
    {
        return explode(',', $data['numbers']);
    }
    
    /**
     * 设置号码
     */
    public function setNumbersAttr($value)
    {
        if (is_array($value)) {
            return implode(',', $value);
        }
        return $value;
    }

    /**
     * 设置开奖时间
     */
    public function setDrawTimeAttr($value)
    {
        if (is_numeric($value)) {
            // 如果是时间戳，转换为datetime格式
            return date('Y-m-d H:i:s', $value);
        }
        return $value;
    }

    /**
     * 设置下期开奖时间
     */
    public function setNextDrawTimeAttr($value)
    {
        if (is_numeric($value)) {
            // 如果是时间戳，转换为datetime格式
            return date('Y-m-d H:i:s', $value);
        }
        return $value;
    }
    
    /**
     * 根据彩种获取最新开奖记录
     */
    public static function getLatestByType($lotteryType)
    {
        return self::where('lottery_type', $lotteryType)
                   ->where('status', self::STATUS_ENABLED)
                   ->order('id', 'desc')
                   ->find();
    }
    
    /**
     * 根据彩种获取开奖列表
     */
    public static function getListByType($lotteryType, $limit = 10)
    {
        return self::where('lottery_type', $lotteryType)
                   ->where('status', self::STATUS_ENABLED)
                   ->order('draw_time', 'desc')
                   ->limit($limit)
                   ->select();
    }
    
    /**
     * 验证号码格式
     */
    public static function validateNumbers($numbers, $requireSeven = true)
    {
        if (is_string($numbers)) {
            $numbers = explode(',', $numbers);
        }

        if (!is_array($numbers) || count($numbers) < 1) {
            return false;
        }

        // 如果要求必须7个号码
        if ($requireSeven && count($numbers) !== 7) {
            return false;
        }

        foreach ($numbers as $number) {
            $num = intval(trim($number));
            if ($num < 1 || $num > 49) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * 生成下期期数
     */
    public static function generateNextPeriod($currentPeriod)
    {
        // 简单的期数递增逻辑
        $num = intval($currentPeriod);
        return strval($num + 1);
    }
}
