<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$site.sicai}开奖记录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 530px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #1e0a0a;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }

        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
        }

        .back-btn img {
            width: auto;
            height: 20px;
            vertical-align: middle;
            filter: brightness(0) invert(1);
        }

        /* 记录列表 */
        .record-list {
            padding: 15px;
        }

        .record-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .period-info {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .draw-time {
            font-size: 12px;
            color: #666;
        }

        /* 号码球样式 */
        .balls-container {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: nowrap;
            overflow-x: auto;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .ball-wrapper {
            position: relative;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-weight: 900;
            font-size: 14px;
            text-shadow: none;
        }

        .ball-info {
            text-align: center;
        }

        .zodiac-element {
            font-size: 10px;
            color: #666;
            white-space: nowrap;
        }

        .plus-separator {
            font-size: 20px;
            font-weight: bold;
            color: #666;
            margin: 0 5px;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .page-btn:hover {
            background: #f8f9fa;
        }

        .page-btn.active {
            background: #00d1ff;
            color: white;
            border-color: #00d1ff;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-info {
            font-size: 14px;
            color: #666;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }

            .ball-wrapper {
                width: 32px;
                height: 32px;
            }

            .ball-number {
                font-size: 12px;
                font-weight: 900;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <a href="/" class="back-btn">
                <img src="/images/balk.png" alt="返回">
            </a>
            <div class="page-title">{$site.sicai}开奖记录</div>
        </div>

        <!-- 记录列表 -->
        <div class="record-list" id="recordList">
            <div class="loading">加载中...</div>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <button class="page-btn" id="prevBtn" onclick="loadPage(currentPage - 1)">上一页</button>
            <span class="page-info" id="pageInfo">第 1 页</span>
            <button class="page-btn" id="nextBtn" onclick="loadPage(currentPage + 1)">下一页</button>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        const pageSize = 20;

        // 获取号码属性（生肖、五行、波色）
        function getNumberAttributes(num) {
            const number = parseInt(num);

            // 生肖对应关系
            const zodiacMap = {
                6: '鼠', 18: '鼠', 30: '鼠', 42: '鼠',
                5: '牛', 17: '牛', 29: '牛', 41: '牛',
                4: '虎', 16: '虎', 28: '虎', 40: '虎',
                3: '兔', 15: '兔', 27: '兔', 39: '兔',
                2: '龙', 14: '龙', 26: '龙', 38: '龙',
                1: '蛇', 13: '蛇', 25: '蛇', 37: '蛇', 49: '蛇',
                12: '马', 24: '马', 36: '马', 48: '马',
                11: '羊', 23: '羊', 35: '羊', 47: '羊',
                10: '猴', 22: '猴', 34: '猴', 46: '猴',
                9: '鸡', 21: '鸡', 33: '鸡', 45: '鸡',
                8: '狗', 20: '狗', 32: '狗', 44: '狗',
                7: '猪', 19: '猪', 31: '猪', 43: '猪'
            };

            // 五行对应关系
            const elementMap = {
                3: '金', 4: '金', 11: '金', 12: '金', 25: '金', 26: '金', 33: '金', 34: '金', 41: '金', 42: '金',
                7: '木', 8: '木', 15: '木', 16: '木', 23: '木', 24: '木', 37: '木', 38: '木', 45: '木', 46: '木',
                13: '水', 14: '水', 21: '水', 22: '水', 29: '水', 30: '水', 43: '水', 44: '水',
                1: '火', 2: '火', 9: '火', 10: '火', 17: '火', 18: '火', 31: '火', 32: '火', 39: '火', 40: '火', 47: '火', 48: '火',
                5: '土', 6: '土', 19: '土', 20: '土', 27: '土', 28: '土', 35: '土', 36: '土', 49: '土'
            };

            // 波色对应关系
            const colorMap = {
                1: 'red', 2: 'red', 7: 'red', 8: 'red', 12: 'red', 13: 'red', 18: 'red', 19: 'red',
                23: 'red', 24: 'red', 29: 'red', 30: 'red', 34: 'red', 35: 'red', 40: 'red', 45: 'red', 46: 'red',

                3: 'blue', 4: 'blue', 9: 'blue', 10: 'blue', 14: 'blue', 15: 'blue', 20: 'blue', 25: 'blue',
                26: 'blue', 31: 'blue', 36: 'blue', 37: 'blue', 41: 'blue', 42: 'blue', 47: 'blue', 48: 'blue',

                5: 'green', 6: 'green', 11: 'green', 16: 'green', 17: 'green', 21: 'green', 22: 'green', 27: 'green',
                28: 'green', 32: 'green', 33: 'green', 38: 'green', 39: 'green', 43: 'green', 44: 'green', 49: 'green'
            };

            return {
                number: num.toString().padStart(2, '0'),
                zodiac: zodiacMap[number] || '未知',
                element: elementMap[number] || '未知',
                color: colorMap[number] || 'blue'
            };
        }

        // 加载开奖记录
        async function loadRecords(page = 1) {
            try {
                const response = await fetch(`/api/lottery/getHistory?page=${page}&limit=${pageSize}&type=sicai`);
                const result = await response.json();

                if (result.code === 1 && result.data) {
                    renderRecords(result.data.list || []);
                    updatePagination(result.data.total || 0, page);
                } else {
                    showNoData();
                }
            } catch (error) {
                showError('加载失败，请稍后重试');
            }
        }

        // 渲染记录列表
        function renderRecords(records) {
            const recordList = document.getElementById('recordList');

            if (!records || records.length === 0) {
                showNoData();
                return;
            }

            let html = '';
            records.forEach(record => {
                const numbers = record.num.split(',');
                let ballsHtml = '';

                numbers.forEach((num, index) => {
                    const ballData = getNumberAttributes(num.trim());
                    ballsHtml += `
                        <div class="ball-item">
                            <div class="ball-wrapper">
                                <img src="/images/ball-${ballData.color}.png" alt="${ballData.color} ball" class="ball-image">
                                <div class="ball-number">${ballData.number}</div>
                            </div>
                            <div class="ball-info">
                                <div class="zodiac-element">${ballData.zodiac}/${ballData.element}</div>
                            </div>
                        </div>
                    `;

                    // 在第6个球后面添加加号分隔符
                    if (index === 5) {
                        ballsHtml += '<div class="plus-separator">+</div>';
                    }
                });

                html += `
                    <div class="record-item">
                        <div class="record-header">
                            <div class="period-info">第${record.periods}期</div>
                            <div class="draw-time">${formatTime(record.draw_time)}</div>
                        </div>
                        <div class="balls-container">
                            ${ballsHtml}
                        </div>
                    </div>
                `;
            });

            recordList.innerHTML = html;
        }

        // 格式化时间（只显示年月日）
        function formatTime(timeStr) {
            if (!timeStr) return '';
            const date = new Date(timeStr);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 更新分页
        function updatePagination(total, page) {
            currentPage = page;
            totalPages = Math.ceil(total / pageSize);

            const pagination = document.getElementById('pagination');
            const pageInfo = document.getElementById('pageInfo');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            if (totalPages > 1) {
                pagination.style.display = 'flex';
                pageInfo.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
                prevBtn.disabled = currentPage <= 1;
                nextBtn.disabled = currentPage >= totalPages;
            } else {
                pagination.style.display = 'none';
            }
        }

        // 加载指定页
        function loadPage(page) {
            if (page < 1 || page > totalPages) return;
            loadRecords(page);
        }

        // 显示无数据
        function showNoData() {
            const recordList = document.getElementById('recordList');
            recordList.innerHTML = '<div class="no-data">暂无开奖记录</div>';
            document.getElementById('pagination').style.display = 'none';
        }

        // 显示错误
        function showError(message) {
            const recordList = document.getElementById('recordList');
            recordList.innerHTML = `<div class="no-data">${message}</div>`;
            document.getElementById('pagination').style.display = 'none';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadRecords(1);
        });
    </script>
</body>
</html>
