<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 530px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 注册页面 */
        .register-page {
            width: 300px;
            background: white;
            padding: 30px;
        }

        .register-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 25px;
            color: #333;
        }

        .register-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .required {
            color: red;
        }

        .register-input {
            padding: 8px 10px;
            border: 1px solid #ccc;
            font-size: 14px;
            outline: none;
        }

        .register-input:focus {
            border-color: #007bff;
        }

        .register-submit {
            padding: 10px 20px;
            background: #ffc107;
            color: #333;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
        }

        .register-submit:hover {
            background: #e0a800;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        /* 消息提示样式 */
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .loading-message {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* 禁用状态 */
        .register-submit:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 注册页面 -->
        <div class="register-page">
            <div class="register-title">用户注册</div>
            <form class="register-form" onsubmit="submitRegister(event)">
                <div class="form-group">
                    <label>用户名 <span class="required">*</span></label>
                    <input type="text" id="registerUsername" class="register-input" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label>密&nbsp;&nbsp;&nbsp;&nbsp;码 <span class="required">*</span></label>
                    <input type="password" id="registerPassword" class="register-input" placeholder="请输入密码" required>
                </div>
                <div class="form-group">
                    <label>确认密码 <span class="required">*</span></label>
                    <input type="password" id="registerConfirmPassword" class="register-input" placeholder="请再次输入密码" required>
                </div>
                <div id="registerMessage"></div>
                <button type="submit" id="registerBtn" class="register-submit">提交注册</button>
            </form>
            <div class="back-link">
                <a href="/index/index/login">已有账号？去登录</a> |
                <a href="/">返回首页</a>
            </div>
        </div>
    </div>

    <script>
        // 显示消息
        function showMessage(message, type = 'error') {
            const messageDiv = document.getElementById('registerMessage');
            messageDiv.innerHTML = `<div class="message ${type}-message">${message}</div>`;

            // 3秒后自动清除消息
            if (type !== 'loading') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 3000);
            }
        }

        // 设置按钮状态
        function setButtonState(disabled, text) {
            const btn = document.getElementById('registerBtn');
            btn.disabled = disabled;
            btn.textContent = text;
        }

        // 验证表单
        function validateForm() {
            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value.trim();
            const confirmPassword = document.getElementById('registerConfirmPassword').value.trim();

            if (!username) {
                showMessage('请输入用户名');
                return false;
            }

            if (username.length < 3) {
                showMessage('用户名至少需要3个字符');
                return false;
            }

            if (!password) {
                showMessage('请输入密码');
                return false;
            }

            if (password.length < 6) {
                showMessage('密码至少需要6个字符');
                return false;
            }

            if (password !== confirmPassword) {
                showMessage('密码和确认密码不一致');
                return false;
            }

            return true;
        }

        // 提交注册
        async function submitRegister(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }

            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value.trim();

            // 显示加载状态
            setButtonState(true, '注册中...');
            showMessage('正在注册，请稍候...', 'loading');

            try {
                const response = await fetch('/api/user/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.code === 1) {
                    // 注册成功
                    showMessage('注册成功！正在跳转到登录页面...', 'success');

                    // 2秒后跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/index/index/login';
                    }, 2000);
                } else {
                    // 注册失败
                    showMessage(result.msg || '注册失败，请稍后重试');
                    setButtonState(false, '提交注册');
                }
            } catch (error) {
                console.error('注册请求失败:', error);
                showMessage('网络错误，请稍后重试');
                setButtonState(false, '提交注册');
            }
        }
    </script>
</body>
</html>
