-- 手动开奖表
CREATE TABLE `fa_lottery_draws` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `lottery_type` varchar(20) NOT NULL DEFAULT '' COMMENT '彩种类型(sicai,xinao,xianggang,laoao)',
  `period` varchar(50) NOT NULL DEFAULT '' COMMENT '期数',
  `numbers` varchar(100) NOT NULL DEFAULT '' COMMENT '开奖号码(逗号分隔)',
  `draw_time` datetime NOT NULL COMMENT '开奖时间',
  `next_period` varchar(50) NOT NULL DEFAULT '' COMMENT '下期期数',
  `next_draw_time` datetime DEFAULT NULL COMMENT '下期开奖时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作管理员ID',
  `admin_name` varchar(50) NOT NULL DEFAULT '' COMMENT '操作管理员名称',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `lottery_type` (`lottery_type`),
  KEY `period` (`period`),
  KEY `draw_time` (`draw_time`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手动开奖记录表';

-- 插入一些示例数据
INSERT INTO `fa_lottery_draws` (`lottery_type`, `period`, `numbers`, `draw_time`, `next_period`, `next_draw_time`, `status`, `admin_id`, `admin_name`, `remark`, `createtime`, `updatetime`) VALUES
('sicai', '200', '08,15,23,31,42,07,19', '2025-08-01 21:32:00', '201', '2025-08-01 22:32:00', 1, 1, 'admin', '私彩开奖', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('xinao', '212', '05,12,25,35,36,20,18', '2025-08-01 20:15:00', '213', '2025-08-01 21:15:00', 1, 1, 'admin', '新澳门开奖', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('xianggang', '089', '01,14,27,33,45,09,22', '2025-08-01 19:45:00', '090', '2025-08-01 20:45:00', 1, 1, 'admin', '香港开奖', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('laoao', '234', '05,16,28,37,44,11,29', '2025-08-01 22:10:00', '235', '2025-08-01 23:10:00', 1, 1, 'admin', '老澳门开奖', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
