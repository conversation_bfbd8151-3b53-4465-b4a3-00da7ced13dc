<?php

namespace app\api\controller;

use app\common\controller\Api;

/**
 * 开奖数据接口
 */
class Lottery extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 获取新澳门开奖数据
     */
    public function getAmData()
    {
        return $this->getLotteryData('https://zhibo.66kj.vip/kj/data/am.js');
    }

    /**
     * 获取香港开奖数据
     */
    public function getXgData()
    {
        return $this->getLotteryData('https://zhibo.77kj.vip/kj/data/xg.js');
    }

    /**
     * 获取老澳门开奖数据
     */
    public function get48AmData()
    {
        return $this->getLotteryData('https://zhibo.77kj.vip/kj/data/48am.js');
    }

    /**
     * 获取手动开奖数据
     */
    public function getManualData()
    {
        $lotteryType = $this->request->get('type', 'sicai');

        try {
            // 直接使用Db类查询，避免模型问题
            $draw = \think\Db::table('fa_lottery_draws')
                ->where('lottery_type', $lotteryType)
                ->where('status', 1)
                ->order('id', 'desc')
                ->find();

            if (!$draw) {
                return json([
                    'code' => 0,
                    'msg' => '暂无开奖数据',
                    'time' => time(),
                    'data' => null
                ]);
            }

            // 获取彩种名称（从配置文件读取）
            $siteConfig = config('site.');
            $typeNames = [
                'sicai' => $siteConfig['sicai'] ?? '私彩',
                'xinao' => '新澳门',
                'xianggang' => '香港',
                'laoao' => '老澳门'
            ];

            // 转换为前端需要的格式
            $data = [
                'title' => $typeNames[$draw['lottery_type']] ?? $draw['lottery_type'],
                'periods' => $draw['period'],
                'num' => $draw['numbers'],
                'times' => $draw['draw_time'],
                'nextperiods' => $draw['next_period'],
                'nexttime' => $draw['next_draw_time']
            ];

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'time' => time(),
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取开奖历史记录
     */
    public function getHistory()
    {
        $lotteryType = $this->request->get('type', 'sicai');
        $limit = $this->request->get('limit', 10);
        $page = $this->request->get('page', 1);

        try {
            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 查询开奖记录（支持分页）
            $list = \think\Db::table('fa_lottery_draws')
                ->where('lottery_type', $lotteryType)
                ->where('status', 1)
                ->order('draw_time', 'desc')
                ->limit($offset, $limit)
                ->select();

            // 查询总数
            $total = \think\Db::table('fa_lottery_draws')
                ->where('lottery_type', $lotteryType)
                ->where('status', 1)
                ->count();

            $data = [];
            foreach ($list as $item) {
                $data[] = [
                    'periods' => $item['period'],  // 统一字段名
                    'num' => $item['numbers'],     // 统一字段名
                    'draw_time' => $item['draw_time'],
                    'remark' => $item['remark']
                ];
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'time' => time(),
                'data' => [
                    'list' => $data,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 通用获取开奖数据方法
     */
    private function getLotteryData($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            $this->error('网络请求失败: ' . $error);
            return;
        }

        if ($httpCode !== 200) {
            $this->error('API请求失败，状态码: ' . $httpCode);
            return;
        }

        if (empty($response)) {
            $this->error('获取数据为空');
            return;
        }

        // 解析JSONP格式的数据
        if (preg_match('/\{.*\}/', $response, $matches)) {
            $jsonData = $matches[0];
            $data = json_decode($jsonData, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('数据解析失败: ' . json_last_error_msg());
                return;
            }

            // 返回成功数据
            $this->success('获取成功', $data);
        } else {
            $this->error('数据格式不正确');
        }
    }

    /**
     * 获取私彩开奖记录
     */
    public function getSicaiRecords()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        try {
            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 查询私彩开奖记录
            $records = Db::name('manual_lottery_draw')
                ->where('lottery_type', 'sicai')
                ->order('id desc')
                ->limit($offset, $limit)
                ->select();

            // 查询总数
            $total = Db::name('manual_lottery_draw')
                ->where('lottery_type', 'sicai')
                ->count();

            // 格式化数据
            $list = [];
            foreach ($records as $record) {
                $list[] = [
                    'id' => $record['id'],
                    'periods' => $record['periods'],
                    'num' => $record['num'],
                    'draw_time' => $record['draw_time'],
                    'created_at' => $record['created_at']
                ];
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
}
